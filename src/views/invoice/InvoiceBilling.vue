<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import {
  getPostPaidInvoiceList,
  batchPreInvoice,
  preInvoice,
  getInvoiceRecords,
  type BatchPreInvoiceRequest,
  type PreInvoiceRequest,
  type InvoiceRecordItem,
  type InvoiceRecordParams,
} from "../../services/invoice";
import type {
  PostPaidInvoiceItem,
  PostPaidInvoiceSearchParams,
} from "../../types/invoice";
import { useToast } from "primevue/usetoast";
import {
  formatChargeMonth,
  isoFormatYYmm,
  formatDateTime,
} from "../../utils/common";
import { getCustomersSimpleList } from "../../services/customer";
import type { CustomerSimpleInfo } from "../../types/customer";
import PrePaidInvoicing from "./PrePaidInvoicing.vue";
import NoInvoicing from "./NoInvoicing.vue";
import PreInvoicing from "./PreInvoicing.vue";
import { formatInvoiceType, formatInvoiceState } from "../../utils/const";

const toast = useToast();
const loading = ref(false);
const items = ref<PostPaidInvoiceItem[]>([]);
const totalRecords = ref(0);

// 筛选表单
const searchForm = ref<PostPaidInvoiceSearchParams>({
  account_seq: "",
  charge_month: undefined,
  customer_name: "",
  sub_order_no: "",
  page: 1,
  pageSize: 20,
});

// 日历控件的日期对象
const chargeMonthDate = ref<Date | null>(null);

// 处理权责账期日期变化
const onChargeMonthChange = (date: Date | null) => {
  chargeMonthDate.value = date;
  if (date) {
    searchForm.value.charge_month = parseInt(isoFormatYYmm(date) || "0");
  } else {
    searchForm.value.charge_month = undefined;
  }
};

// 当前选中的Tab状态
const activeTab = ref("pending-post-paid");

// Tab切换处理函数
const handleTabChange = (tabKey: string) => {
  activeTab.value = tabKey;
  loadCurrentTabData();
};

// 判断Tab是否处于激活状态
const isActiveTab = (item: any): boolean => {
  // 对于有子菜单的项目，检查子菜单中是否有激活的项目
  if (item.items && item.items.length > 0) {
    return item.items.some(
      (subItem: any) => subItem.tabKey === activeTab.value
    );
  }

  // 对于没有子菜单的项目，直接检查tabKey
  return item.tabKey === activeTab.value;
};

// Menubar的菜单项配置 - 遵循苹果设计规范的层级结构
const menuItems = ref([
  {
    label: "待开票",
    icon: "pi pi-clock",
    items: [
      {
        label: "后付费",
        icon: "pi pi-credit-card",
        tabKey: "pending-post-paid",
        command: () => handleTabChange("pending-post-paid"),
      },
      {
        label: "预付费",
        icon: "pi pi-wallet",
        tabKey: "pending-pre-paid",
        command: () => handleTabChange("pending-pre-paid"),
      },
      {
        label: "不开票",
        icon: "pi pi-ban",
        tabKey: "pending-no-invoice",
        command: () => handleTabChange("pending-no-invoice"),
      },
    ],
  },
  {
    label: "预开票",
    icon: "pi pi-check-circle",
    tabKey: "pre-invoiced",
    command: () => handleTabChange("pre-invoiced"),
  },
  {
    label: "待提交",
    icon: "pi pi-upload",
    tabKey: "pending-submit",
    command: () => handleTabChange("pending-submit"),
  },
  {
    label: "已审核",
    icon: "pi pi-verified",
    tabKey: "reviewed",
    command: () => handleTabChange("reviewed"),
  },
]);

// 计算当前页面标题
const pageTitle = computed(() => {
  const tabMap: Record<string, string> = {
    "pending-post-paid": "待开票 - 后付费",
    "pending-pre-paid": "待开票 - 预付费",
    "pending-no-invoice": "待开票 - 不开票",
    "pre-invoiced": "预开票",
    "pending-submit": "待提交",
    reviewed: "已审核",
  };
  return tabMap[activeTab.value] || "发票管理";
});

// 计算是否显示内容区域（后付费和预付费Tab显示数据）
const showContent = computed(() => {
  return activeTab.value === "pending-post-paid";
});

// 计算是否显示预付费内容
const showPrePaidContent = computed(() => {
  return activeTab.value === "pending-pre-paid";
});

// 计算是否显示不开票内容
const showNoInvoiceContent = computed(() => {
  return activeTab.value === "pending-no-invoice";
});

// 计算是否显示预开票内容
const showPreInvoicingContent = computed(() => {
  return activeTab.value === "pre-invoiced";
});

// 计算是否可以进行预开票（选中项目的分账序号必须一致）
const canPreInvoice = computed(() => {
  if (selectedItems.value.length === 0) return false;

  const firstAccountSeq = selectedItems.value[0].account_seq;
  return selectedItems.value.every(
    (item) => item.account_seq === firstAccountSeq
  );
});

// 批量预开票相关状态
const batchPreInvoiceDialogVisible = ref(false);
const batchPreInvoiceLoading = ref(false);
const customerOptions = ref<CustomerSimpleInfo[]>([]);

// 批量预开票表单数据
interface BatchPreInvoiceFormData {
  customer_num: string | null;
  account_seq: string | null;
  start_charge_month: number;
  end_charge_month: number;
}

const batchPreInvoiceForm = ref<BatchPreInvoiceFormData>({
  customer_num: "",
  account_seq: "",
  start_charge_month: 0,
  end_charge_month: 0,
});

// 账期周期日期范围
const chargeCycleDates = ref<Date[] | null>(null);

// 处理账期周期日期范围变化
const onChargeCycleDatesChange = (
  value: Date | Date[] | (Date | null)[] | null | undefined
) => {
  const dates = Array.isArray(value) ? (value as Date[]) : null;
  chargeCycleDates.value = dates;
  if (dates && dates.length === 2 && dates[0] && dates[1]) {
    batchPreInvoiceForm.value.start_charge_month = parseInt(
      isoFormatYYmm(dates[0]) || "0"
    );
    batchPreInvoiceForm.value.end_charge_month = parseInt(
      isoFormatYYmm(dates[1]) || "0"
    );
  } else {
    batchPreInvoiceForm.value.start_charge_month = 0;
    batchPreInvoiceForm.value.end_charge_month = 0;
  }
};

// 表单验证错误
const formErrors = ref<Record<string, string>>({});

// 选中的项目
const selectedItems = ref<PostPaidInvoiceItem[]>([]);

// 预开票相关状态
const preInvoiceDialogVisible = ref(false);
const preInvoiceLoading = ref(false);
const exchangeRate = ref<number | null>(null);
const exchangeRateError = ref<string>("");

// 开票记录相关状态
const invoiceRecordDialogVisible = ref(false);
const invoiceRecordLoading = ref(false);
const invoiceRecords = ref<InvoiceRecordItem[]>([]);
const invoiceRecordTotalRecords = ref(0);
const invoiceRecordSearchParams = ref<InvoiceRecordParams>({
  page: 1,
  pageSize: 10,
});

// 加载客户列表
const loadCustomerOptions = async () => {
  try {
    const response = await getCustomersSimpleList();
    if (response.code === 200) {
      customerOptions.value = response.data;
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载客户列表失败",
      life: 3000,
    });
  }
};

// 打开批量预开票对话框
const openBatchPreInvoiceDialog = () => {
  batchPreInvoiceDialogVisible.value = true;
  loadCustomerOptions();
  // 重置表单
  batchPreInvoiceForm.value = {
    customer_num: "",
    account_seq: "",
    start_charge_month: 0,
    end_charge_month: 0,
  };
  chargeCycleDates.value = null;
  formErrors.value = {};
};

// 关闭批量预开票对话框
const closeBatchPreInvoiceDialog = () => {
  batchPreInvoiceDialogVisible.value = false;
  formErrors.value = {};
};

// 表单验证
const validateBatchPreInvoiceForm = (): boolean => {
  formErrors.value = {};
  if (
    !batchPreInvoiceForm.value.start_charge_month ||
    !batchPreInvoiceForm.value.end_charge_month
  ) {
    formErrors.value.charge_cycle = "请选择账期周期";
  }

  return Object.keys(formErrors.value).length === 0;
};

// 提交批量预开票
const submitBatchPreInvoice = async () => {
  if (!validateBatchPreInvoiceForm()) {
    return;
  }

  batchPreInvoiceLoading.value = true;
  try {
    const requestData: BatchPreInvoiceRequest = {
      customer_num: batchPreInvoiceForm.value.customer_num!,
      account_seq: batchPreInvoiceForm.value.account_seq!,
      start_charge_month: batchPreInvoiceForm.value.start_charge_month,
      end_charge_month: batchPreInvoiceForm.value.end_charge_month,
    };

    const response = await batchPreInvoice(requestData);

    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "批量预开票操作成功",
        life: 3000,
      });

      closeBatchPreInvoiceDialog();
      // 刷新数据
      loadCurrentTabData();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "批量预开票操作失败",
        life: 3000,
      });
    }
  } catch (error: any) {
    console.error("批量预开票失败:", error);

    // 处理422验证错误
    if (error.response?.status === 422 && error.response?.data?.fields) {
      const fields = error.response.data.fields;
      Object.keys(fields).forEach((field) => {
        if (fields[field] && fields[field].length > 0) {
          formErrors.value[field] = fields[field][0].message;
        }
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: error.response?.data?.message || "批量预开票操作失败",
        life: 3000,
      });
    }
  } finally {
    batchPreInvoiceLoading.value = false;
  }
};

// 获取数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm.value,
      page: searchForm.value.page || 1,
      pageSize: searchForm.value.pageSize || 20,
    };

    // 移除空值参数
    Object.keys(params).forEach((key) => {
      const paramKey = key as keyof typeof params;
      if (params[paramKey] === "" || params[paramKey] === undefined) {
        delete (params as any)[paramKey];
      }
    });

    const response = await getPostPaidInvoiceList(params);
    if (response.code === 200) {
      items.value = response.data.records;
      totalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "获取数据失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取数据失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  searchForm.value.page = 1;
  loadData();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    account_seq: "",
    charge_month: undefined,
    customer_name: "",
    sub_order_no: "",
    page: 1,
    pageSize: 20,
  };
  chargeMonthDate.value = null;
  loadData();
};

// 分页变化
const onPageChange = (event: any) => {
  searchForm.value.page = event.page + 1;
  searchForm.value.pageSize = event.rows;
  loadData();
};

// 根据当前Tab状态加载数据
const loadCurrentTabData = () => {
  if (activeTab.value === "pending-post-paid") {
    loadData();
  } else if (activeTab.value === "pending-pre-paid") {
    // 预付费Tab由独立组件处理，不需要额外操作
    items.value = [];
    totalRecords.value = 0;
  } else if (activeTab.value === "pending-no-invoice") {
    // 不开票Tab由独立组件处理，不需要额外操作
    items.value = [];
    totalRecords.value = 0;
  } else if (activeTab.value === "pre-invoiced") {
    // 预开票Tab由独立组件处理，不需要额外操作
    items.value = [];
    totalRecords.value = 0;
  } else {
    // 其他Tab暂时清空数据
    items.value = [];
    totalRecords.value = 0;

    const tabLabels: Record<string, string> = {
      "pending-submit": "待提交",
      reviewed: "已审核",
    };

    toast.add({
      severity: "info",
      summary: "提示",
      detail: `${tabLabels[activeTab.value] || "当前"}功能开发中`,
      life: 3000,
    });
  }
};

// 格式化金额
const formatAmount = (amount: string) => {
  return `¥${parseFloat(amount).toLocaleString()}`;
};

// 打开预开票对话框
const openPreInvoiceDialog = () => {
  if (selectedItems.value.length === 0) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择要预开票的记录",
      life: 3000,
    });
    return;
  }

  if (!canPreInvoice.value) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "选中的记录分账序号不一致，无法进行预开票",
      life: 3000,
    });
    return;
  }

  preInvoiceDialogVisible.value = true;
  exchangeRate.value = null;
  exchangeRateError.value = "";
};

// 关闭预开票对话框
const closePreInvoiceDialog = () => {
  preInvoiceDialogVisible.value = false;
  exchangeRate.value = null;
  exchangeRateError.value = "";
};

// 验证汇率
const validateExchangeRate = (): boolean => {
  exchangeRateError.value = "";

  if (!exchangeRate.value || exchangeRate.value <= 0) {
    exchangeRateError.value = "请输入有效的汇率";
    return false;
  }

  return true;
};

// 提交预开票
const submitPreInvoice = async () => {
  if (!validateExchangeRate()) {
    return;
  }

  preInvoiceLoading.value = true;
  try {
    const requestData: PreInvoiceRequest = {
      charge_detail_ids: selectedItems.value.map((item) => item.id),
      exchange_rate: exchangeRate.value!.toString(),
    };

    const response = await preInvoice(requestData);

    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "预开票操作成功",
        life: 3000,
      });

      closePreInvoiceDialog();
      selectedItems.value = [];
      // 刷新数据
      loadCurrentTabData();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "预开票操作失败",
        life: 3000,
      });
    }
  } catch (error: any) {
    console.error("预开票失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: error.response?.data?.message || "预开票操作失败",
      life: 3000,
    });
  } finally {
    preInvoiceLoading.value = false;
  }
};

// 获取开票记录数据
const loadInvoiceRecords = async () => {
  invoiceRecordLoading.value = true;
  try {
    const params = {
      ...invoiceRecordSearchParams.value,
      page: invoiceRecordSearchParams.value.page || 1,
      pageSize: invoiceRecordSearchParams.value.pageSize || 10,
    };

    const response = await getInvoiceRecords(params);
    if (response.code === 200) {
      invoiceRecords.value = response.data.records;
      invoiceRecordTotalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "获取开票记录失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("获取开票记录失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取开票记录失败",
      life: 3000,
    });
  } finally {
    invoiceRecordLoading.value = false;
  }
};

// 打开开票记录对话框
const openInvoiceRecordDialog = () => {
  invoiceRecordDialogVisible.value = true;
  invoiceRecordSearchParams.value = {
    page: 1,
    pageSize: 10,
  };
  loadInvoiceRecords();
};

// 关闭开票记录对话框
const closeInvoiceRecordDialog = () => {
  invoiceRecordDialogVisible.value = false;
  invoiceRecords.value = [];
  invoiceRecordTotalRecords.value = 0;
};

// 开票记录分页变化
const onInvoiceRecordPageChange = (event: any) => {
  invoiceRecordSearchParams.value.page = event.page + 1;
  invoiceRecordSearchParams.value.pageSize = event.rows;
  loadInvoiceRecords();
};

onMounted(() => {
  loadCurrentTabData();
});
</script>

<template>
  <div class="invoice-container">
    <div class="card">
      <!-- Menubar导航 -->
      <div class="menubar-container mb-3">
        <Menubar :model="menuItems" class="invoice-menubar">
          <template #item="{ item, props, hasSubmenu, root }">
            <a
              v-ripple
              class="flex items-center"
              v-bind="props.action"
              :class="{ active: isActiveTab(item) }"
            >
              <span :class="item.icon" class="mr-2"></span>
              <span class="font-medium">{{ item.label }}</span>
              <i
                v-if="hasSubmenu"
                :class="[
                  'pi pi-angle-down ml-auto',
                  { 'pi-angle-down': root, 'pi-angle-right': !root },
                ]"
              ></i>
            </a>
          </template>
          <template #end>
            <div class="menubar-actions">
              <Badge :value="items.length" class="mr-2" severity="info" />
              <span class="text-sm text-muted-color">条记录</span>
            </div>
          </template>
        </Menubar>
      </div>

      <!-- 内容区域 -->
      <div v-if="showContent">
        <!-- 搜索工具栏 -->
        <Toolbar class="mb-2">
          <template #start>
            <div class="flex items-center gap-4">
              <Message severity="info">待开票-后付费</Message>
            </div>
          </template>
          <template #end>
            <div class="flex flex-wrap gap-2 items-center">
              <FloatLabel>
                <label class="text-sm font-medium text-gray-700 mb-1"
                  >订单编号</label
                >
                <InputText v-model="searchForm.sub_order_no" class="w-48" />
              </FloatLabel>
              <FloatLabel>
                <label class="text-sm font-medium text-gray-700 mb-1"
                  >客户名称</label
                >
                <InputText v-model="searchForm.customer_name" class="w-48" />
              </FloatLabel>
              <FloatLabel>
                <label class="text-sm font-medium text-gray-700 mb-1"
                  >权责账期</label
                >
                <DatePicker
                  v-model="chargeMonthDate"
                  view="month"
                  dateFormat="yy-mm"
                  showIcon
                  class="w-48"
                  @date-select="onChargeMonthChange"
                />
              </FloatLabel>
              <FloatLabel>
                <label class="text-sm font-medium text-gray-700 mb-1"
                  >分账序号</label
                >
                <InputText v-model="searchForm.account_seq" class="w-48" />
              </FloatLabel>
              <Button @click="handleSearch" icon="pi pi-search" rounded />
              <Button
                @click="handleReset"
                icon="pi pi-refresh"
                class="p-button-secondary"
                rounded
              />
            </div>
            <Divider layout="vertical" />
            <Button
              icon="pi pi-file"
              label="预开票"
              @click="openPreInvoiceDialog"
              :disabled="!canPreInvoice"
              severity="success"
              class="mr-2"
            />
            <Button
              icon="pi pi-file-edit"
              label="批量预开票"
              @click="openBatchPreInvoiceDialog"
              severity="warn"
            />
            <Divider layout="vertical" />
            <Button
              icon="pi pi-history"
              @click="openInvoiceRecordDialog"
              severity="info"
              outlined
            />
          </template>
        </Toolbar>

        <!-- 数据表格 -->
        <DataTable
          :value="items"
          v-model:selection="selectedItems"
          :loading="loading"
          :paginator="true"
          :rows="20"
          :totalRecords="totalRecords"
          :lazy="true"
          @page="onPageChange"
          paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
          :rowsPerPageOptions="[10, 20, 50]"
          currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
          class="p-datatable-sm"
          showGridlines
          scrollable
          scrollHeight="calc(100vh - 25rem)"
          dataKey="id"
        >
          <template #empty>
            <div class="empty-message">
              <i
                class="pi pi-inbox"
                style="
                  font-size: 2rem;
                  color: var(--p-text-color-secondary);
                  margin-bottom: 1rem;
                "
              ></i>
              <p>暂无待开票数据</p>
            </div>
          </template>

          <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>

          <Column
            field="sub_order_no"
            header="订单编号"
            style="min-width: 15rem"
          >
            <template #body="{ data }">
              <span class="font-mono text-sm">{{ data.sub_order_no }}</span>
            </template>
          </Column>

          <Column
            field="customer_name"
            header="客户名称"
            style="min-width: 15rem"
          >
            <template #body="{ data }">
              <span class="font-medium">{{ data.customer_name }}</span>
            </template>
          </Column>

          <Column
            field="charge_month"
            header="权责账期"
            style="min-width: 10rem"
          >
            <template #body="{ data }">
              <span>{{ formatChargeMonth(data.charge_month) }}</span>
            </template>
          </Column>

          <Column
            field="account_seq"
            header="分账序号"
            style="min-width: 10rem"
          >
            <template #body="{ data }">
              <span class="font-mono text-sm">{{ data.account_seq }}</span>
            </template>
          </Column>

          <Column field="fee_amount" header="费用金额" style="min-width: 10rem">
            <template #body="{ data }">
              <span class="font-semibold text-green-600">{{
                formatAmount(data.fee_amount)
              }}</span>
            </template>
          </Column>

          <Column field="tax_type" header="税目" style="min-width: 8rem">
            <template #body="{ data }">
              <Tag :value="data.tax_type" class="p-tag-info" />
            </template>
          </Column>

          <Column field="income_type" header="收入类型" style="min-width: 8rem">
            <template #body="{ data }">
              <Tag :value="data.income_type" class="p-tag-secondary" />
            </template>
          </Column>

          <Column field="pay_type" header="付费类型" style="min-width: 8rem">
            <template #body="{ data }">
              <Tag
                :value="data.pay_type"
                :class="
                  data.pay_type === '预付' ? 'p-tag-success' : 'p-tag-warning'
                "
              />
            </template>
          </Column>

          <Column
            field="invoice_amount"
            header="已开票金额"
            style="min-width: 8rem"
          >
            <template #body="{ data }">
              <span class="font-semibold text-blue-600">{{
                formatAmount(data.invoice_amount)
              }}</span>
            </template>
          </Column>

          <Column
            field="adjust_month"
            header="调账月份"
            style="min-width: 8rem"
          >
            <template #body="{ data }">
              <span v-if="data.adjust_month">{{
                formatChargeMonth(data.adjust_month)
              }}</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </Column>
        </DataTable>
      </div>

      <!-- 预付费开票内容 -->
      <div v-else-if="showPrePaidContent">
        <PrePaidInvoicing />
      </div>

      <!-- 不开票内容 -->
      <div v-else-if="showNoInvoiceContent">
        <NoInvoicing />
      </div>

      <!-- 预开票内容 -->
      <div v-else-if="showPreInvoicingContent">
        <PreInvoicing />
      </div>

      <!-- 其他Tab的占位内容 -->
      <div v-else class="empty-state">
        <div class="empty-state-content">
          <i class="pi pi-cog empty-state-icon"></i>
          <h3 class="empty-state-title">功能开发中</h3>
          <p class="empty-state-description">{{ pageTitle }}功能正在开发中</p>
          <p class="empty-state-subtitle">敬请期待...</p>
        </div>
      </div>
    </div>

    <!-- 批量预开票Dialog -->
    <Dialog
      v-model:visible="batchPreInvoiceDialogVisible"
      modal
      header="批量预开票"
      :style="{ width: '500px' }"
      @hide="closeBatchPreInvoiceDialog"
      :draggable="false"
      :resizable="false"
    >
      <div class="form-section">
        <div class="grid grid-cols-1 gap-4">
          <!-- 客户选择 -->
          <div class="field">
            <label for="customer_num">客户</label>
            <Select
              v-model="batchPreInvoiceForm.customer_num"
              :options="customerOptions"
              optionLabel="customer_name"
              optionValue="customer_num"
              class="w-full"
              showClear
              filter
            />
          </div>

          <!-- 分账序号 -->
          <div class="field">
            <label for="account_seq">分账序号</label>
            <InputText
              v-model="batchPreInvoiceForm.account_seq"
              class="w-full"
            />
          </div>

          <!-- 账期周期 -->
          <div class="field">
            <label for="charge_cycle" class="required">账期周期</label>
            <DatePicker
              v-model="chargeCycleDates"
              selectionMode="range"
              :numberOfMonths="2"
              view="month"
              dateFormat="yy-mm"
              placeholder="选择账期周期范围"
              class="w-full"
              :class="{ 'p-invalid': formErrors.charge_cycle }"
              @update:modelValue="onChargeCycleDatesChange"
              showIcon
            />
            <small v-if="formErrors.charge_cycle" class="p-error">
              {{ formErrors.charge_cycle }}
            </small>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <Button
            label="取消"
            icon="pi pi-times"
            @click="closeBatchPreInvoiceDialog"
            class="p-button-secondary"
          />
          <Button
            label="预开票"
            icon="pi pi-check"
            @click="submitBatchPreInvoice"
            :loading="batchPreInvoiceLoading"
            class="p-button-warn"
          />
        </div>
      </template>
    </Dialog>

    <!-- 预开票Dialog -->
    <Dialog
      v-model:visible="preInvoiceDialogVisible"
      modal
      header="预开票"
      :style="{ width: '400px' }"
      @hide="closePreInvoiceDialog"
      :draggable="false"
      :resizable="false"
    >
      <div class="form-section">
        <div class="grid grid-cols-1 gap-4">
          <!-- 选中项目信息 -->
          <div class="field">
            <label>选中项目</label>
            <div class="selected-items-info">
              <p class="text-sm text-gray-600 mb-2">
                已选择 {{ selectedItems.length }} 条记录
              </p>
              <p class="text-sm text-gray-600" v-if="selectedItems.length > 0">
                分账序号: {{ selectedItems[0].account_seq }}
              </p>
            </div>
          </div>

          <!-- 汇率输入 -->
          <div class="field">
            <label for="exchange_rate" class="required">汇率</label>
            <InputNumber
              v-model="exchangeRate"
              :minFractionDigits="0"
              :maxFractionDigits="10"
              placeholder="请输入汇率"
              class="w-full"
              :class="{ 'p-invalid': exchangeRateError }"
            />
            <small v-if="exchangeRateError" class="p-error">
              {{ exchangeRateError }}
            </small>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <Button
            label="取消"
            icon="pi pi-times"
            @click="closePreInvoiceDialog"
            class="p-button-secondary"
          />
          <Button
            label="确认预开票"
            icon="pi pi-check"
            @click="submitPreInvoice"
            :loading="preInvoiceLoading"
            class="p-button-success"
          />
        </div>
      </template>
    </Dialog>

    <!-- 开票记录Dialog -->
    <Dialog
      v-model:visible="invoiceRecordDialogVisible"
      modal
      header="开票记录"
      :style="{ width: '90vw', maxWidth: '1200px' }"
      @hide="closeInvoiceRecordDialog"
      :draggable="false"
      :resizable="false"
    >
      <div class="invoice-record-content">
        <!-- 开票记录表格 -->
        <DataTable
          :value="invoiceRecords"
          :loading="invoiceRecordLoading"
          :paginator="true"
          :rows="10"
          :totalRecords="invoiceRecordTotalRecords"
          :lazy="true"
          @page="onInvoiceRecordPageChange"
          paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
          :rowsPerPageOptions="[10, 20, 50]"
          currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
          class="p-datatable-sm"
          showGridlines
          scrollable
          scrollHeight="60vh"
        >
          <template #empty>
            <div class="empty-message">
              <i
                class="pi pi-inbox"
                style="
                  font-size: 2rem;
                  color: var(--p-text-color-secondary);
                  margin-bottom: 1rem;
                "
              ></i>
              <p>暂无开票记录</p>
            </div>
          </template>
          <Column
            field="task_id"
            header="任务ID"
            style="min-width: 12rem; max-width: 12rem"
          >
            <template #body="{ data }">
              <span
                class="font-mono text-xs omitted-text"
                :title="data.task_id"
                v-tooltip.top="data.task_id"
              >
                {{ data.task_id }}
              </span>
            </template>
          </Column>

          <Column
            field="invoice_type"
            header="开票类型"
            style="min-width: 10rem"
          >
            <template #body="{ data }">
              <Tag
                :value="formatInvoiceType(data.invoice_type)"
                :class="
                  data.invoice_type === 1 ? 'p-tag-success' : 'p-tag-warning'
                "
              />
            </template>
          </Column>

          <Column field="state" header="状态" style="min-width: 10rem">
            <template #body="{ data }">
              <Tag
                :value="formatInvoiceState(data.state).label"
                :severity="formatInvoiceState(data.state).severity"
              />
            </template>
          </Column>

          <Column
            field="request_info"
            header="请求信息"
            style="min-width: 15rem"
          >
            <template #body="{ data }">
              <div class="request-info">
                <div v-if="data.request_info.customer_num" class="info-item">
                  <span class="info-label">客户编号:</span>
                  <span class="info-value">{{
                    data.request_info.customer_num
                  }}</span>
                </div>
                <div v-if="data.request_info.account_seq" class="info-item">
                  <span class="info-label">分账序号:</span>
                  <span class="info-value">{{
                    data.request_info.account_seq
                  }}</span>
                </div>
                <div
                  v-if="
                    data.request_info.start_charge_month &&
                    data.request_info.end_charge_month
                  "
                  class="info-item"
                >
                  <span class="info-label">账期:</span>
                  <span class="info-value"
                    >{{
                      formatChargeMonth(data.request_info.start_charge_month)
                    }}
                    -
                    {{
                      formatChargeMonth(data.request_info.end_charge_month)
                    }}</span
                  >
                </div>
                <div v-if="data.request_info.exchange_rate" class="info-item">
                  <span class="info-label">汇率:</span>
                  <span class="info-value">{{
                    data.request_info.exchange_rate
                  }}</span>
                </div>
                <div
                  v-if="
                    data.request_info.charge_detail_ids &&
                    data.request_info.charge_detail_ids.length > 0
                  "
                  class="info-item"
                >
                  <span class="info-label">明细ID:</span>
                  <span class="info-value">{{
                    data.request_info.charge_detail_ids.join(", ")
                  }}</span>
                </div>
              </div>
            </template>
          </Column>

          <Column field="create_user" header="创建人" style="min-width: 10rem">
            <template #body="{ data }">
              <span class="font-medium">{{ data.create_user }}</span>
            </template>
          </Column>

          <Column field="updated_at" header="更新时间" style="min-width: 15rem">
            <template #body="{ data }">
              <span class="text-sm">{{ formatDateTime(data.updated_at) }}</span>
            </template>
          </Column>

          <Column field="reason" header="失败原因" style="min-width: 10rem">
            <template #body="{ data }">
              <span
                v-if="data.reason"
                class="text-red-600 text-sm omitted-text"
                >{{ data.reason }}</span
              >
              <span v-else class="text-gray-400">-</span>
            </template>
          </Column>
        </DataTable>
      </div>
    </Dialog>

    <Toast />
  </div>
</template>

<style scoped>
.invoice-container {
  padding: 1rem;
  background: #f8f9fa;
  height: calc(100vh - 10rem);
}

/* Menubar容器样式 */
.menubar-container {
  display: flex;
  justify-content: flex-start;
}

.menubar-actions {
  display: flex;
  align-items: center;
}

/* 自定义Menubar样式 */
:deep(.invoice-menubar) {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
}

:deep(.invoice-menubar .p-menubar-root-list) {
  gap: 0.25rem;
}

:deep(.invoice-menubar .p-menuitem-link) {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-weight: 500;
}

:deep(.invoice-menubar .p-menuitem-link.active) {
  background: #3b82f6;
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

:deep(.invoice-menubar .p-menuitem-link.active:hover) {
  background: #2563eb;
  color: #ffffff;
}

:deep(.invoice-menubar .p-menubar-submenu) {
  z-index: 100;
}

:deep(.invoice-menubar .p-menuitem-link:focus) {
  background: #e0f2fe;
  color: #0369a1;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

:deep(.invoice-menubar .p-submenu-list) {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  margin-top: 0.25rem;
}

:deep(.invoice-menubar .p-submenu-list .p-menuitem-link) {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

:deep(.invoice-menubar .p-submenu-list .p-menuitem-link:hover) {
  background: #f0f9ff;
  color: #0369a1;
}

:deep(.invoice-menubar .p-submenu-list .p-menuitem-link.active) {
  background: #3b82f6;
  color: #ffffff;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
}

:deep(.invoice-menubar .p-submenu-list .p-menuitem-link.active:hover) {
  background: #2563eb;
  color: #ffffff;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
}

.empty-state-content {
  text-align: center;
  max-width: 400px;
}

.empty-state-icon {
  font-size: 4rem;
  color: #d1d5db;
  margin-bottom: 1.5rem;
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.empty-state-description {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 0.5rem 0;
}

.empty-state-subtitle {
  font-size: 0.875rem;
  color: #9ca3af;
  margin: 0;
}

/* 工具栏和表格样式 */
:deep(.p-toolbar) {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #f1f5f9;
  color: #475569;
  font-weight: 600;
  border-color: #e2e8f0;
}

:deep(.p-datatable .p-datatable-tbody > tr:nth-child(odd)) {
  background: #f8fafc;
}

:deep(.p-datatable .p-datatable-tbody > tr:hover) {
  background: #e0f2fe;
}

:deep(.p-tag) {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.form-section {
  margin-bottom: 1rem;
}

.field {
  margin-bottom: 0.5rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--p-primary-color);
  letter-spacing: -0.025em;
}

.field label.required::after {
  content: " *";
  color: #ff3b30;
  font-weight: 600;
  margin-left: 2px;
}

.p-error {
  color: #ff3b30;
  font-weight: 400;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.2;
}

.selected-items-info {
  background: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 6px;
  padding: 0.75rem;
  margin-top: 0.25rem;
}

.selected-items-info p {
  margin: 0;
  line-height: 1.4;
}

/* 开票记录相关样式 */
.invoice-record-content {
  padding: 0;
}

.request-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.info-label {
  font-weight: 500;
  color: #6b7280;
  min-width: 4rem;
  flex-shrink: 0;
}

.info-value {
  color: #374151;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  font-size: 0.8rem;
}

/* 文本省略样式 */
.omitted-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
</style>
